/**
 * @file 字典表单组件
 * @description 根据字典类型动态渲染表单字段，包括编码、名称、描述、父级选择、排序、状态等
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */

import DictSelect from '@/components/DictSelect';
import type { FormInstance } from 'antd';
import { Col, Form, Input, InputNumber, Row, Switch } from 'antd';
import React from 'react';
import { FORM_RULES } from '../constants';
import type { DictType } from '../dict-types';

const { TextArea } = Input;

interface DictFormProps {
  form: FormInstance;
  type: DictType;
}

export const DictForm: React.FC<DictFormProps> = ({
  form,
  type,
}) => {
  const renderFormFields = () => {
    switch (type) {
      case 'region':
        return (
          <>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="regionName"
                  label="区域名称"
                  rules={FORM_RULES.name}
                >
                  <Input placeholder="请输入区域名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="regionCode"
                  label="区域编码"
                  rules={FORM_RULES.code}
                >
                  <Input placeholder="请输入区域编码" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item name="regionDesc" label="区域描述">
              <TextArea rows={3} placeholder="请输入区域描述" />
            </Form.Item>
          </>
        );
      case 'type':
        return (
          <>
            <Form.Item name="typeCode" label="类型编码" rules={FORM_RULES.code}>
              <Input placeholder="请输入类型编码" />
            </Form.Item>
            <Form.Item name="typeName" label="类型名称" rules={FORM_RULES.name}>
              <Input placeholder="请输入类型名称" />
            </Form.Item>
            <Form.Item name="typeDesc" label="类型描述">
              <TextArea rows={3} placeholder="请输入类型描述" />
            </Form.Item>
          </>
        );
      case 'relation':
        return (
          <>
            <Form.Item
              name="relationCode"
              label="关系编码"
              rules={FORM_RULES.code}
            >
              <Input placeholder="请输入关系编码" />
            </Form.Item>
            <Form.Item
              name="relationName"
              label="关系名称"
              rules={FORM_RULES.name}
            >
              <Input placeholder="请输入关系名称" />
            </Form.Item>
            <Form.Item name="relationDesc" label="关系描述">
              <TextArea rows={3} placeholder="请输入关系描述" />
            </Form.Item>
          </>
        );
      default:
        return null;
    }
  };

  return (
    <Form form={form} layout="vertical">
      {renderFormFields()}
      <Row gutter={16}>
        <Col span={18}>
          <Form.Item name="parentId" label="父级字典">
            <DictSelect.DictTreeSelect
              type={type}
              style={{ width: '100%' }}
              placeholder="请选择父级字典（留空表示顶级）"
              allowClear
              treeDefaultExpandAll
              showSearch
            />
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item name="sort" label="排序" rules={FORM_RULES.sort}>
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入排序号"
              min={1}
            />
          </Form.Item>
        </Col>
      </Row>
      <Form.Item name="status" label="状态" valuePropName="checked">
        <Switch checkedChildren="启用" unCheckedChildren="禁用" />
      </Form.Item>
    </Form>
  );
};
